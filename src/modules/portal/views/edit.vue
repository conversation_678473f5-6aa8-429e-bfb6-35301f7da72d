<template>
	<div class="wrapper">
		<div class="form-container">
			<el-form :model="form" label-width="100px" :rules="rules" ref="formRef">
				<el-form-item label="展示图" :required="true" prop="pic">
					<div v-if="form.pic?.length > 0" class="material">
							<el-image
								style="width: 120px; height: 80px"
								:src="form.pic"
								fit="cover"
							/>
							<el-icon class="del" size="20" @click="delPic()"
								><Delete
							/></el-icon>
						</div>
						<el-icon
							v-else
							class="cursor-pointer"
							size="30"
							color="#CFD3DC"
							@click="addPic()"
							><CirclePlus
						/></el-icon>
						<input
							type="file"
							multiple
							accept="image/*"
							:ref="setRefs('fileInput')"
							style="display: none"
							@change="handleFileChange($event, 1)"
						/>
				</el-form-item>

				<el-form-item label="类型" :required="true" prop="type">
					<el-radio-group v-model="form.type">
						<el-radio :label="0">标题摘要</el-radio>
						<el-radio :label="1">通栏图片</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item label="标题" :required="true" prop="title">
					<el-input v-model="form.title" placeholder="请输入标题" />
				</el-form-item>

				<el-form-item label="摘要" :required="true" prop="summary">
					<el-input
						type="textarea"
						v-model="form.summary"
						placeholder="请输入摘要"
						rows="3"
					/>
				</el-form-item>

				<el-form-item label="外链" :required="form.isExternalLink" prop="externalLink">
					<div style="display: flex; align-items: center; gap: 8px; width: 100%;">
						<el-checkbox v-model="form.isExternalLink" @change="handleExternalLinkChange" style="flex-shrink: 0;" />
						<el-input
							v-model="form.externalLink"
							placeholder="请输入外链地址"
							:disabled="!form.isExternalLink"
							style="flex: 1; min-width: 0;"
						/>
					</div>
				</el-form-item>

				<el-form-item label="内容" :required="!form.isExternalLink" prop="content">
					<div id="vditor" class="vditor-container"></div>
					<div v-if="form.isExternalLink" class="external-link-overlay">
						<span>启用外链时，内容编辑器不可用</span>
					</div>
				</el-form-item>

				<el-form-item label="Thread ID" :required="!form.thread && !form.isExternalLink" prop="forumTid">
					<el-input v-model="form.forumTid" placeholder="请输入帖子链接或帖子ID，例如: https://forum.chasedream.com/thread-1397043-1-1.html" clearable :disabled="form.isExternalLink" /><br />
					<el-checkbox v-model="form.thread" label="新建帖子" size="large" :disabled="form.isExternalLink" />
				</el-form-item>

				<el-form-item label="帖子版块" v-if="form.thread">
					<el-row :gutter="20" style="width: 100%">
						<el-col :span="8">
							<el-form-item prop="fid">
								<el-select
									v-model="form.fid"
									placeholder="请选择"
									@change="navChange"
								>
									<el-option-group
										v-for="group in navs"
										:key="group.fid"
										:label="group.name"
									>
										<el-option
											v-for="item in group.forums"
											:key="item.fid"
											:label="item.name"
											:value="item.fid"
										/>
									</el-option-group>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item prop="typeid">
								<el-select v-model="form.typeid" placeholder="请选择">
									<el-option
										v-for="obj in subNavs"
										:key="obj.typeid"
										:label="obj.name"
										:value="obj.typeid"
									/>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form-item>
				<el-form-item label="发帖账号" prop="uid" v-if="form.thread">
					<el-radio-group v-model="form.uid" @change="uidChange">
						<el-radio
							v-for="obj in forumAccount"
							:key="obj.uid"
							:label="obj.username"
							:value="obj.uid"
						></el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item label="作者" :required="!form.isExternalLink" prop="author">
					<el-input v-model="form.author" placeholder="请输入作者" :disabled="form.isExternalLink" />
				</el-form-item>

				<el-form-item label="分类" :required="true" prop="category">
					<el-radio-group v-model="form.category">
						<el-radio label="MBA">MBA</el-radio>
						<el-radio label="GMAT">GMAT</el-radio>
						<el-radio label="Master">Master</el-radio>
						<el-radio label="PhD">PhD</el-radio>						
					</el-radio-group>
				</el-form-item>

				<el-form-item label="排序" prop="displayOrder">
					<el-select v-model="form.displayOrder" placeholder="请选择排序">
						<el-option :value="3" label="3" />
						<el-option :value="2" label="2" />
						<el-option :value="1" label="1" />
						<el-option :value="0" label="0" />
					</el-select>
				</el-form-item>

				<el-form-item label="标签" prop="tags">
					<TagSelector
						v-model="tagSelectorData"
						@tagsChange="handleTagsChange"
					/>
				</el-form-item>

				<el-form-item style="margin-top: 40px">
					<el-button type="primary" @click="handleSubmit(formRef)">提交</el-button>
					<el-button @click="router.back()">取消</el-button>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue";
import { CirclePlus, Delete } from "@element-plus/icons-vue";
import { forumAccount } from "../../event/data/index";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { useCool } from "/@/cool";
import { isDev } from "/@/config";
import _ from "lodash";
import Vditor from "vditor";
import "vditor/dist/index.css";
import TagSelector from "../components/TagSelector.vue";

// 类型定义
interface NavItem {
	fid: string;
	name: string;
	forums?: NavItem[];
}

interface SubNavItem {
	typeid: string;
	name: string;
}

const { service, router, refs, setRefs } = useCool();

let vd = ref();

const cUrl = isDev
				? "http://localhost:9000/dev/admin/base/open/upload4PortalPic"
				: "https://connect.chasedream.com/api/v2/admin/base/open/upload4PortalPic";

const form = reactive({
	id: 0,
	pic: "",
	title: "",
	summary: "",
	content: "",
	forumTid: "",
	author: "",
	category: "",
	tags: "",
	tagsVal: "",
	majorTags: "",
	majorTagsVal: "",
	schoolTags: "",
	schoolTagsVal: "",
	fid: "",
	typeid: "",
	uid: "",
	username: "",
	thread: false,
	type: 0,
	isExternalLink: false,
	externalLink: "",
	displayOrder: 0,
});

const navs = ref<NavItem[]>([]);
const subNavs = ref<SubNavItem[]>([]);

// 标签选择器数据
const tagSelectorData = ref({
	tags: "",
	tagsVal: "",
	majorTags: "",
	majorTagsVal: "",
	schoolTags: "",
	schoolTagsVal: ""
});

const rules = reactive({
	pic: [{ required: true, message: "请上传展示图", trigger: "blur" }],
	type: [{ required: true, message: "请选择类型", trigger: "change" }],
	title: [{ required: true, message: "请输入标题", trigger: "blur" }],
	summary: [{ required: true, message: "请输入摘要", trigger: "blur" }],
	content: [{
		required: () => !form.isExternalLink,
		message: "请输入内容",
		trigger: "blur"
	}],
	forumTid: [{
		required: () => !form.thread && !form.isExternalLink,
		message: "请输入帖子ID",
		trigger: "blur"
	}],
	author: [{
		required: () => !form.isExternalLink,
		message: "请输入作者",
		trigger: "blur"
	}],
	category: [{ required: true, message: "请选择分类", trigger: "change" }],
	externalLink: [{
		required: () => form.isExternalLink,
		message: "请输入外链地址",
		trigger: "blur"
	}, {
		validator: (rule: any, value: string, callback: any) => {
			if (form.isExternalLink && value) {
				callback();
			} else {
				callback();
			}
		},
		trigger: "blur"
	}]
});

const formRef = ref<FormInstance>();

const loadNav = async () => {
	service.base.common.forum
		.nav()
		.then((res) => {
			navs.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const navChange = () => {
	service.base.common.forum
		.subnav({
			fid: form.fid
		})
		.then((res) => {
			subNavs.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const uidChange = (uid) => {
	const account = forumAccount.find((obj) => obj.uid === uid);
	form.username = account?.username || "";
};

const handleExternalLinkChange = () => {
	// 只清除相关字段的验证状态，让验证规则重新生效
	// 不清空字段值，保留用户之前输入的内容
	nextTick(() => {
		if (formRef.value) {
			formRef.value.clearValidate(['content', 'forumTid', 'author', 'externalLink']);
		}
	});
};

// 处理标签变更
const handleTagsChange = (tagsData: any) => {
	form.tags = tagsData.tags;
	form.tagsVal = tagsData.tagsVal;
	form.majorTags = tagsData.majorTags;
	form.majorTagsVal = tagsData.majorTagsVal;
	form.schoolTags = tagsData.schoolTags;
	form.schoolTagsVal = tagsData.schoolTagsVal;
};

const initVditor = async () => {
  await nextTick();
  vd.value = new Vditor("vditor", {
    height: 400,
    width: '100%',
    mode: 'sv',
    preview: {
      mode: 'both',
      hljs: {
        style: 'github'
      }
    },
    cache: {
      enable: false
    },
    after: () => {
	  loadData();
    },
    input: (value) => {
      form.content = value;
    },
    upload: {
      url: cUrl,
      accept: 'image/*',
      success: (editor: HTMLPreElement, msg: string) => {
        const response = JSON.parse(msg);
        if (response.code === 1000 && response.data.success) {
          const imageUrl = response.data.image;
          // Insert image URL into editor
          vd.value.insertValue(`![image](${imageUrl})`);
        }
      }
    }
  });
};

const handleSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	
	form.content = vd.value.getValue();

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		service.base.common.portal
			.portalUpdate({
				...form
			})
			.then((res) => {
				ElMessage({
					message: "已修改!",
					type: "success"
				});
				router.back();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const handleFileChange = async (event: any, type) => {
	const files = event.target.files;
	if (files) {
		await upload(files, type);
	}

	event.target.value = "";
};

const upload = async (files: any, type) => {
	for (let i = 0; i < files.length; i++) {
		const formData = new FormData();
		formData.append("files", files[i]);

		try {
			const url = isDev
				? "http://localhost:9000/dev/admin/base/open/upload4PortalPic"
				: "https://connect.chasedream.com/api/v2/admin/base/open/upload4PortalPic";

			const response = await fetch(url, {
				method: "POST",
				body: formData
			});

			if (response.ok) {
				const res = await response.json();
				if (type === 1) {
					form.pic = res.data.image;
				} else if (type === 2) {
					form.pic = res.data.image;
				}
			} else {
				ElMessage.error("Upload failed");
			}
		} catch (err: any) {
			ElMessage.error(err.message);
		}
	}
};

const delPic = () => {
	form.pic = "";
};

const addPic = () => {
	const el = refs[`fileInput`];
	el?.click();
};







// 数据加载相关方法
const getQueryParam = (param) => {
	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get(param);
};

const loadData = async () => {
    service.base.common.portal
        .portalFindOne({
            id: getQueryParam("id")
        })
        .then((res) => {
			form.id = res.id;
            form.pic = res.pic;
            form.title = res.title;
            form.summary = res.summary;
            form.content = res.content;
            form.forumTid = res.forumTid;
            form.author = res.author;
            form.category = res.category || "GMAT";
            form.tags = res.tags || "";
            form.tagsVal = res.tagsVal || "";
            form.majorTags = res.majorTags || res.professionalTags || "";
            form.majorTagsVal = res.majorTagsVal || res.professionalTagsVal || "";
            form.schoolTags = res.schoolTags || "";
            form.schoolTagsVal = res.schoolTagsVal || "";
            form.type = res.type || res.summaryType || 0;
            form.externalLink = res.externalLink || "";
            form.displayOrder = res.displayOrder || 0;
            // 如果外链有值，自动勾选外链复选框
            form.isExternalLink = res.isExternalLink || (res.externalLink && res.externalLink.trim() !== "");

			// 延迟设置Vditor内容，确保实例已经初始化
			nextTick(() => {
				if (vd.value && vd.value.setValue) {
					vd.value.setValue(form.content);
				}
			});

			// 初始化标签选择器数据
			tagSelectorData.value = {
				tags: form.tags,
				tagsVal: form.tagsVal,
				majorTags: form.majorTags,
				majorTagsVal: form.majorTagsVal,
				schoolTags: form.schoolTags,
				schoolTagsVal: form.schoolTagsVal
			};
        })
        .catch((err) => {
            ElMessage.error(err.message);
        });
};

onMounted(async () => {
	loadNav();
    await initVditor();
	// 等待一小段时间确保Vditor完全初始化
	setTimeout(() => {
		loadData();
	}, 100);
});

</script>

<style lang="scss" scoped>
@import "../css/index.scss";



.external-link-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(245, 247, 250, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	border-radius: 4px;
}

.external-link-overlay span {
	background-color: #fff;
	padding: 8px 16px;
	border-radius: 4px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	color: #666;
	font-size: 14px;
}

.vditor-container {
	position: relative;
}
</style>
