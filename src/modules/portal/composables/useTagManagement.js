import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { useCool } from "/@/cool";

export function useTagManagement() {
    const { service } = useCool();

    // 标签数据
    const availableTags = ref([]);
    const selectedTags = ref([]);
    const processedTags = ref([]);

    // 专业方向标签
    const availableMajorTags = ref([]);
    const selectedMajorTags = ref([]);
    const processedMajorTags = ref([]);

    // 学校名称标签
    const selectedSchoolTags = ref([]);
    const schoolSearchData = ref([]);
    const schoolSearchLoading = ref(false);
    const schoolTagInput = ref({
        id: "",
        name: ""
    });

    // 弹窗状态
    const tagDialogVisible = ref(false);
    const tempSelectedTagIds = ref([]);
    const tempSelectedMajorTagIds = ref([]);

    // 标签颜色配置
    const tagColors = [
        '#d32f2f', '#9c3dc4', '#673ab7', '#3f51b5', '#2196f3',
        '#00a9bb', '#007467', '#29892d', '#8bc34a', '#afb42b',
        '#ffbf00', '#f88a1a', '#ff5722', '#795548', '#c2c2c2'
    ];

    // 加载可用标签
    const loadAvailableTags = async () => {
        try {
            const res = await service.base.common.portal.getAvailableTags({
                property: 3
            });
            availableTags.value = res.sort((a, b) => {
                const parseVersion = (order) => {
                    if (!order) return [3, 5, 999, 999];
                    const parts = order.split('.').map(Number);
                    return [parts[0] || 0, parts[1] || 0, parts[2] || 0, parts[3] || 0];
                };

                const versionA = parseVersion(a.order);
                const versionB = parseVersion(b.order);

                if (versionA[1] !== versionB[1]) {
                    return versionA[1] - versionB[1];
                }
                if (versionA[2] !== versionB[2]) {
                    return versionA[2] - versionB[2];
                }
                return versionA[3] - versionB[3];
            });

            processTagsWithSeparators();
        } catch (err) {
            ElMessage.error(err.message);
        }
    };

    // 加载专业方向标签
    const loadAvailableMajorTags = async () => {
        try {
            const res = await service.base.common.portal.getAvailableTags({
                property: 2
            });
            availableMajorTags.value = res.sort((a, b) => {
                const parseVersion = (order) => {
                    if (!order) return [3, 5, 999, 999];
                    const parts = order.split('.').map(Number);
                    return [parts[0] || 0, parts[1] || 0, parts[2] || 0, parts[3] || 0];
                };

                const versionA = parseVersion(a.order);
                const versionB = parseVersion(b.order);

                if (versionA[1] !== versionB[1]) {
                    return versionA[1] - versionB[1];
                }
                if (versionA[2] !== versionB[2]) {
                    return versionA[2] - versionB[2];
                }
                return versionA[3] - versionB[3];
            });

            processMajorTagsWithSeparators();
        } catch (err) {
            ElMessage.error(err.message);
        }
    };

    // 处理标签分隔符
    const processTagsWithSeparators = () => {
        const processed = [];
        const encounteredVersions = new Set();

        for (const tag of availableTags.value) {
            let shouldAddSeparator = false;
            let separatorType = '';

            if (tag.order) {
                if ((tag.order === "3.3" || tag.order.match(/^3\.3\.\d+$/)) && !encounteredVersions.has('3.3')) {
                    shouldAddSeparator = true;
                    separatorType = 'double';
                    encounteredVersions.add('3.3');
                }
                else if (tag.order.match(/^3\.5\.\d+$/) && !encounteredVersions.has('3.5')) {
                    shouldAddSeparator = true;
                    separatorType = 'single';
                    encounteredVersions.add('3.5');
                }
                else if (tag.order.match(/^3\.6\.\d+$/) && !encounteredVersions.has('3.6')) {
                    shouldAddSeparator = true;
                    separatorType = 'single';
                    encounteredVersions.add('3.6');
                }
            }

            if (shouldAddSeparator) {
                processed.push({
                    type: 'separator',
                    breakType: separatorType,
                    id: `separator-${processed.length}`
                });
            }

            processed.push({
                type: 'tag',
                ...tag
            });
        }

        processedTags.value = processed;
    };

    // 处理专业方向标签分隔符
    const processMajorTagsWithSeparators = () => {
        const processed = [];
        const encounteredVersions = new Set();

        for (const tag of availableMajorTags.value) {
            let shouldAddSeparator = false;
            let separatorType = '';

            if (tag.order) {
                if (tag.order.match(/^3\.5\.\d+$/) && !encounteredVersions.has('3.5')) {
                    shouldAddSeparator = true;
                    separatorType = 'double';
                    encounteredVersions.add('3.5');
                }
                else if (tag.order.match(/^3\.6\.\d+$/) && !encounteredVersions.has('3.6')) {
                    shouldAddSeparator = true;
                    separatorType = 'single';
                    encounteredVersions.add('3.6');
                }
            }

            if (shouldAddSeparator) {
                processed.push({
                    type: 'separator',
                    breakType: separatorType,
                    id: `separator-${processed.length}`
                });
            }

            processed.push({
                type: 'tag',
                ...tag
            });
        }

        processedMajorTags.value = processed;
    };

    // 学校名称标签搜索
    const handleSchoolSearch = async (query) => {
        schoolSearchData.value = [];
        if (query !== "") {
            schoolSearchLoading.value = true;
            try {
                const res = await service.base.common.portal.tagSearch({
                    s: query,
                    property: 1
                });
                if (res && res.length > 0) {
                    schoolSearchData.value = res.map((item) => ({
                        ...item,
                        synonyms: item.synonym ? item.synonym.map((s) => s.keyword || s.name) : []
                    }));
                }
            } catch (err) {
                ElMessage.error(err.message || "搜索失败");
            } finally {
                schoolSearchLoading.value = false;
            }
        }
    };

    // 标签选择相关方法
    const showTagDialog = () => {
        tempSelectedTagIds.value = selectedTags.value.map(tag => tag.id);
        tempSelectedMajorTagIds.value = selectedMajorTags.value.map(tag => tag.id);
        tagDialogVisible.value = true;
    };

    const cancelTagSelection = () => {
        tagDialogVisible.value = false;
        tempSelectedTagIds.value = [];
        tempSelectedMajorTagIds.value = [];
    };

    const confirmTagSelection = () => {
        selectedTags.value = availableTags.value.filter(tag =>
            tempSelectedTagIds.value.includes(tag.id)
        );
        selectedMajorTags.value = availableMajorTags.value.filter(tag =>
            tempSelectedMajorTagIds.value.includes(tag.id)
        );
        tagDialogVisible.value = false;
        tempSelectedTagIds.value = [];
        tempSelectedMajorTagIds.value = [];
        return {
            tags: selectedTags.value,
            majorTags: selectedMajorTags.value
        };
    };

    const toggleTagSelection = (tagId) => {
        const index = tempSelectedTagIds.value.indexOf(tagId);
        if (index > -1) {
            tempSelectedTagIds.value.splice(index, 1);
        } else {
            tempSelectedTagIds.value.push(tagId);
        }
    };

    const toggleMajorTagSelection = (tagId) => {
        const index = tempSelectedMajorTagIds.value.indexOf(tagId);
        if (index > -1) {
            tempSelectedMajorTagIds.value.splice(index, 1);
        } else {
            tempSelectedMajorTagIds.value.push(tagId);
        }
    };

    // 标签删除方法
    const removeTag = (tag) => {
        const index = selectedTags.value.findIndex(t => t.id === tag.id);
        if (index > -1) {
            selectedTags.value.splice(index, 1);
        }
    };

    const removeMajorTag = (tag) => {
        const index = selectedMajorTags.value.findIndex(t => t.id === tag.id);
        if (index > -1) {
            selectedMajorTags.value.splice(index, 1);
        }
    };

    const removeSchoolTag = (tag, index) => {
        selectedSchoolTags.value.splice(index, 1);
    };

    // 学校标签添加
    const onSchoolTagChange = (tagId) => {
        if (tagId) {
            addSchoolTagById(tagId);
            schoolTagInput.value.id = "";
        }
    };

    const addSchoolTagById = (tagId) => {
        const existingTag = schoolSearchData.value.find(item => item.id === tagId);

        if (existingTag) {
            const isAlreadyAdded = selectedSchoolTags.value.some(tag => tag.id === tagId);
            if (!isAlreadyAdded) {
                selectedSchoolTags.value.push({
                    id: existingTag.id,
                    name: existingTag.name
                });
            } else {
                ElMessage.warning("该学校标签已经添加过了！");
            }
        }
    };

    const onSchoolTagClear = () => {
        schoolTagInput.value.id = "";
        schoolTagInput.value.name = "";
        schoolSearchData.value = [];
    };

    // 样式相关方法
    const getTagColorIndex = (index) => {
        return index % tagColors.length;
    };

    const getTagDisplayStyle = (tag, index) => {
        const colorIndex = getTagColorIndex(index);
        const color = tagColors[colorIndex];
        return {
            backgroundColor: color,
            color: '#fff',
            border: `1px solid ${color}`,
        };
    };

    const getTagDialogStyle = (tag, index, isSelected) => {
        const colorIndex = getTagColorIndex(index);
        const color = tagColors[colorIndex];

        if (isSelected) {
            return {
                backgroundColor: color,
                color: '#fff',
                border: `1px solid ${color}`,
            };
        } else {
            return {
                backgroundColor: 'transparent',
                color: color,
                border: `1px solid ${color}`,
            };
        }
    };

    // 初始化选中标签
    const initSelectedTags = (tagsVal, tags) => {
        if (tagsVal) {
            const tagIds = tagsVal.split("|");
            const tagNames = tags ? tags.split("|") : [];

            selectedTags.value = tagIds.map((id, index) => ({
                id: id,
                name: tagNames[index] || ""
            })).filter(tag => tag.name);
        }
    };

    const initSelectedMajorTags = (majorTagsVal, majorTags) => {
        if (majorTagsVal) {
            const tagIds = majorTagsVal.split("|");
            const tagNames = majorTags ? majorTags.split("|") : [];

            selectedMajorTags.value = tagIds.map((id, index) => ({
                id: id,
                name: tagNames[index] || ""
            })).filter(tag => tag.name);
        }
    };

    const initSelectedSchoolTags = (schoolTagsVal, schoolTags) => {
        if (schoolTagsVal) {
            const tagIds = schoolTagsVal.split("|");
            const tagNames = schoolTags ? schoolTags.split("|") : [];

            selectedSchoolTags.value = tagIds.map((id, index) => ({
                id: id,
                name: tagNames[index] || ""
            })).filter(tag => tag.name);
        }
    };

    return {
        // 数据
        availableTags,
        selectedTags,
        processedTags,
        availableMajorTags,
        selectedMajorTags,
        processedMajorTags,
        selectedSchoolTags,
        schoolSearchData,
        schoolSearchLoading,
        schoolTagInput,
        tagDialogVisible,
        tempSelectedTagIds,
        tempSelectedMajorTagIds,

        // 方法
        loadAvailableTags,
        loadAvailableMajorTags,
        handleSchoolSearch,
        showTagDialog,
        cancelTagSelection,
        confirmTagSelection,
        toggleTagSelection,
        toggleMajorTagSelection,
        removeTag,
        removeMajorTag,
        removeSchoolTag,
        onSchoolTagChange,
        onSchoolTagClear,
        getTagDisplayStyle,
        getTagDialogStyle,
        initSelectedTags,
        initSelectedMajorTags,
        initSelectedSchoolTags
    };
}